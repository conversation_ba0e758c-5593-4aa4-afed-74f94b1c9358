<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>总线通信原理 - 互动学习</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(50px);
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .demo-area {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 40px 0;
            min-height: 300px;
        }

        .device {
            width: 120px;
            height: 80px;
            background: linear-gradient(145deg, #f0f0f0, #cacaca);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #333;
            box-shadow: 5px 5px 15px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .device:hover {
            transform: translateY(-5px);
            box-shadow: 5px 10px 25px rgba(0,0,0,0.3);
        }

        .bus-line {
            flex: 1;
            height: 8px;
            background: #ddd;
            margin: 0 20px;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }

        .data-packet {
            width: 30px;
            height: 20px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border-radius: 10px;
            position: absolute;
            top: -6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .explanation {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .quiz-section {
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
            text-align: center;
        }

        .quiz-question {
            font-size: 1.5rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .quiz-option {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-3px);
        }

        .quiz-option.correct {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.3);
        }

        .quiz-option.wrong {
            border-color: #f44336;
            background: rgba(244, 67, 54, 0.3);
        }

        .status-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ccc;
            margin: 0 auto 10px;
            transition: all 0.3s ease;
        }

        .status-indicator.active {
            background: #4CAF50;
            box-shadow: 0 0 20px rgba(76, 175, 80, 0.6);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .pulse {
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚌 总线通信原理</h1>
            <p>让我们用动画来理解半双工和全双工的区别！</p>
        </div>

        <!-- 基础概念介绍 -->
        <div class="learning-section" id="concept-section">
            <h2 class="section-title">📚 基础概念</h2>
            <div class="explanation">
                <h3>什么是总线？</h3>
                <p>总线就像是计算机内部的"高速公路"，用来传输数据。想象一下，两个设备要互相发送信息，就需要通过这条"公路"。</p>
            </div>
        </div>

        <!-- 半双工演示 -->
        <div class="learning-section" id="half-duplex-section">
            <h2 class="section-title">🔄 半双工通信</h2>
            <div class="explanation">
                <h3>半双工特点：</h3>
                <p>• 可以双向传输数据<br>• 但不能同时传输<br>• 就像单车道的桥，一次只能一个方向通行</p>
            </div>
            
            <div class="demo-area">
                <div class="device" id="device-a1">
                    <div>
                        <div class="status-indicator" id="status-a1"></div>
                        设备A
                    </div>
                </div>
                <div class="bus-line" id="bus-half">
                    <div class="data-packet" id="packet-half" style="display: none;">数据</div>
                </div>
                <div class="device" id="device-b1">
                    <div>
                        <div class="status-indicator" id="status-b1"></div>
                        设备B
                    </div>
                </div>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="demonstrateHalfDuplex('A')">A → B 发送</button>
                <button class="btn" onclick="demonstrateHalfDuplex('B')">B → A 发送</button>
                <button class="btn" onclick="resetDemo('half')">重置</button>
            </div>
        </div>

        <!-- 全双工演示 -->
        <div class="learning-section" id="full-duplex-section">
            <h2 class="section-title">⚡ 全双工通信</h2>
            <div class="explanation">
                <h3>全双工特点：</h3>
                <p>• 可以双向传输数据<br>• 可以同时传输<br>• 就像双车道的公路，两个方向可以同时通行</p>
            </div>
            
            <div class="demo-area">
                <div class="device" id="device-a2">
                    <div>
                        <div class="status-indicator" id="status-a2"></div>
                        设备A
                    </div>
                </div>
                <div style="flex: 1; margin: 0 20px;">
                    <div class="bus-line" id="bus-full-1" style="margin-bottom: 10px;">
                        <div class="data-packet" id="packet-full-1" style="display: none;">数据1</div>
                    </div>
                    <div class="bus-line" id="bus-full-2">
                        <div class="data-packet" id="packet-full-2" style="display: none;">数据2</div>
                    </div>
                </div>
                <div class="device" id="device-b2">
                    <div>
                        <div class="status-indicator" id="status-b2"></div>
                        设备B
                    </div>
                </div>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="demonstrateFullDuplex('A')">A → B 发送</button>
                <button class="btn" onclick="demonstrateFullDuplex('B')">B → A 发送</button>
                <button class="btn" onclick="demonstrateFullDuplex('both')">同时发送</button>
                <button class="btn" onclick="resetDemo('full')">重置</button>
            </div>
        </div>

        <!-- 测试题目 -->
        <div class="learning-section quiz-section" id="quiz-section">
            <h2 class="section-title">🎯 知识测试</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progress"></div>
            </div>
            
            <div class="quiz-question">
                下列说法中正确的是（  ）
            </div>
            
            <div class="quiz-options">
                <div class="quiz-option" onclick="selectAnswer(this, false)">
                    <strong>A.</strong> 半双工总线只在一个方向上传输信息，全双工总线可在两个方向上轮流传输信息
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">
                    <strong>B.</strong> 半双工总线只在一个方向上传输信息，全双工总线可在两个方向上同时传输信息
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, true)">
                    <strong>C.</strong> 半双工总线可在两个方向上轮流传输信息，全双工总线可在两个方向上同时传输信息
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">
                    <strong>D.</strong> 半双工总线可在两个方向上同时传输信息，全双工总线可在两个方向上轮流传输信息
                </div>
            </div>
            
            <div id="quiz-result" style="margin-top: 30px; font-size: 1.2rem;"></div>
        </div>
    </div>

    <script>
        // 页面加载动画
        window.addEventListener('load', function() {
            gsap.from('.header h1', {duration: 1, y: -50, opacity: 0, ease: 'bounce.out'});
            gsap.from('.header p', {duration: 1, y: 30, opacity: 0, delay: 0.3});
            
            gsap.to('.learning-section', {
                duration: 0.8,
                opacity: 1,
                y: 0,
                stagger: 0.2,
                ease: 'power2.out',
                delay: 0.5
            });
        });

        // 半双工演示
        function demonstrateHalfDuplex(direction) {
            resetDemo('half');
            
            const packet = document.getElementById('packet-half');
            const statusA = document.getElementById('status-a1');
            const statusB = document.getElementById('status-b1');
            
            packet.style.display = 'block';
            
            if (direction === 'A') {
                statusA.classList.add('active');
                packet.style.left = '0px';
                packet.textContent = '数据→';
                
                gsap.to(packet, {
                    duration: 2,
                    left: 'calc(100% - 30px)',
                    ease: 'power2.inOut',
                    onComplete: function() {
                        statusA.classList.remove('active');
                        statusB.classList.add('active');
                        setTimeout(() => statusB.classList.remove('active'), 1000);
                    }
                });
            } else {
                statusB.classList.add('active');
                packet.style.left = 'calc(100% - 30px)';
                packet.textContent = '←数据';
                
                gsap.to(packet, {
                    duration: 2,
                    left: '0px',
                    ease: 'power2.inOut',
                    onComplete: function() {
                        statusB.classList.remove('active');
                        statusA.classList.add('active');
                        setTimeout(() => statusA.classList.remove('active'), 1000);
                    }
                });
            }
        }

        // 全双工演示
        function demonstrateFullDuplex(direction) {
            resetDemo('full');
            
            const packet1 = document.getElementById('packet-full-1');
            const packet2 = document.getElementById('packet-full-2');
            const statusA = document.getElementById('status-a2');
            const statusB = document.getElementById('status-b2');
            
            if (direction === 'A') {
                packet1.style.display = 'block';
                packet1.style.left = '0px';
                packet1.textContent = '数据→';
                statusA.classList.add('active');
                
                gsap.to(packet1, {
                    duration: 2,
                    left: 'calc(100% - 30px)',
                    ease: 'power2.inOut',
                    onComplete: function() {
                        statusA.classList.remove('active');
                        statusB.classList.add('active');
                        setTimeout(() => statusB.classList.remove('active'), 1000);
                    }
                });
            } else if (direction === 'B') {
                packet2.style.display = 'block';
                packet2.style.left = 'calc(100% - 30px)';
                packet2.textContent = '←数据';
                statusB.classList.add('active');
                
                gsap.to(packet2, {
                    duration: 2,
                    left: '0px',
                    ease: 'power2.inOut',
                    onComplete: function() {
                        statusB.classList.remove('active');
                        statusA.classList.add('active');
                        setTimeout(() => statusA.classList.remove('active'), 1000);
                    }
                });
            } else if (direction === 'both') {
                packet1.style.display = 'block';
                packet2.style.display = 'block';
                packet1.style.left = '0px';
                packet2.style.left = 'calc(100% - 30px)';
                packet1.textContent = '数据→';
                packet2.textContent = '←数据';
                statusA.classList.add('active');
                statusB.classList.add('active');
                
                gsap.to(packet1, {
                    duration: 2,
                    left: 'calc(100% - 30px)',
                    ease: 'power2.inOut'
                });
                
                gsap.to(packet2, {
                    duration: 2,
                    left: '0px',
                    ease: 'power2.inOut',
                    onComplete: function() {
                        statusA.classList.remove('active');
                        statusB.classList.remove('active');
                        setTimeout(() => {
                            statusA.classList.add('active');
                            statusB.classList.add('active');
                            setTimeout(() => {
                                statusA.classList.remove('active');
                                statusB.classList.remove('active');
                            }, 1000);
                        }, 500);
                    }
                });
            }
        }

        // 重置演示
        function resetDemo(type) {
            if (type === 'half') {
                document.getElementById('packet-half').style.display = 'none';
                document.getElementById('status-a1').classList.remove('active');
                document.getElementById('status-b1').classList.remove('active');
            } else {
                document.getElementById('packet-full-1').style.display = 'none';
                document.getElementById('packet-full-2').style.display = 'none';
                document.getElementById('status-a2').classList.remove('active');
                document.getElementById('status-b2').classList.remove('active');
            }
        }

        // 答题功能
        let answered = false;
        
        function selectAnswer(element, isCorrect) {
            if (answered) return;
            
            answered = true;
            const options = document.querySelectorAll('.quiz-option');
            const result = document.getElementById('quiz-result');
            const progress = document.getElementById('progress');
            
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    if (isCorrect) {
                        option.classList.add('correct');
                        result.innerHTML = '🎉 恭喜答对了！<br><br><strong>解析：</strong><br>半双工可以双向传输，但不能同时传输（轮流）<br>全双工可以双向传输，且可以同时传输';
                        result.style.color = '#4CAF50';
                        gsap.to(progress, {duration: 1, width: '100%'});
                    } else {
                        option.classList.add('wrong');
                        result.innerHTML = '❌ 答案不正确，正确答案是C<br><br><strong>解析：</strong><br>半双工：双向但不同时（像单车道桥）<br>全双工：双向且同时（像双车道路）';
                        result.style.color = '#f44336';
                        gsap.to(progress, {duration: 1, width: '60%'});
                    }
                }
            });
            
            // 显示正确答案
            setTimeout(() => {
                options[2].classList.add('correct');
            }, 1000);
            
            // 添加庆祝动画
            if (isCorrect) {
                gsap.to(element, {duration: 0.5, scale: 1.05, yoyo: true, repeat: 3});
            }
        }

        // 设备悬停效果
        document.querySelectorAll('.device').forEach(device => {
            device.addEventListener('mouseenter', function() {
                gsap.to(this, {duration: 0.3, scale: 1.05, rotationY: 5});
            });
            
            device.addEventListener('mouseleave', function() {
                gsap.to(this, {duration: 0.3, scale: 1, rotationY: 0});
            });
        });
    </script>
</body>
</html>
